# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  global_data.py
@Description    :  
@CreateTime     :  2023/8/23 16:46
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/23 16:46
"""
# ---------------------------------------------TMS参数---------------------------------------------
# 司机账户
driver_email = "<EMAIL>"
driver_account_name = "k001"
driver_password = "1234abcd"
driver_user_id = ********
driver_user_name = "k001 - BA-R1a"
# central账户
user_id = 7642085
password = "chuan1992"
# 派车单
dispatch_id = 471802
# ---------------------------------------------WMS参数---------------------------------------------
# WMS 账号设置
wms_user_id = "7226349"
wms_user_password = "123456"
user_name = "janine.cheng.1"

# count
get_count_bin = {
    "warehouse_number": "29",
    "storage_type": "1",
    "location_type": 4,
    "status": 0,
    "module_name": "random_bin_cycle_count",
}
get_count_stock = {
    "warehouse_number": "29",
    "storage_type": "1",
    "location_type": 3,
    "status": 0,
    "module_name": "stock_cycle_count",
    "restock_type": 2,
}
scan_location_count = {
    "warehouse_number": "29",
    "location_no": "E0301-5-5",
    "diff_qty": 5,
}

# packing
normal_packing = {
    "warehouse": "33",
    "tote_no": "TM1051",
    "order_id": "********",
    "storage_type": "1",
}
geekplus_packing = {
    "warehouse": "41",
    "tote_no": "TG0780",
    "order_id": "242513428",
    "storage_type": "1",
}
geekplus_packing_to_replenish = {
    "warehouse": "41",
    "tote_no": "TG0781",
    "order_id": "242513429",
    "storage_type": "1",
}
as_packing_to_replenish = {
    "warehouse": "25",
    "tote_no": "TL0158",
    "order_id": "242508511",
    "storage_type": "1",
    "hot_tote": "H0021",
}
as_packing = {
    "warehouse": "25",
    "tote_no": "TL0135",
    "order_id": "242506772",
    "storage_type": "1",
}
normal_forcestock_packing = {
    "warehouse": "20",
    "tote_no": "TL0004",
    "order_id": "1786780502",
    "storage_type": "2",
}
normal_cancel_packing = {
    "warehouse": "29",
    "tote_no": "TS0336",
    "order_id": "17891564",
    "storage_type": "1",
}
mof_cancel_packing = {
    "warehouse": "29",
    "tote_no": "TM0300",
    "order_id": "242506705",
    "storage_type": "6",
}
sowing_packing = {
    "warehouse": "8",
    "tote_no": "X011",
    "order_id": "24245104902",
    "storage_type": "2",
}
sowing_forcestock_packing = {
    "warehouse": "8",
    "tote_no": "X329",
    "order_id": "1787595502",
    "storage_type": "2",
}
mof_packing = {
    "warehouse": "33",
    "tote_no": "TS0305",
    "order_id": "23838138",
    "storage_type": "6",
}
mof_forcestock_packing = {
    "warehouse": "33",
    "tote_no": "TM0237",
    "order_id": "242503926",
    "storage_type": "6",
}
one_item_packing = {
    "warehouse": "33",
    "cart_no": "OM02",
    "order_id": "17732467",
    "storage_type": "1",
}
mod_packing = {
    "warehouse": "33",
    "tote_no": "TS0004",
    "order_id": "18650464",
    "storage_type": "1",
}
bulk_packing = {
    "warehouse": "33",
    "cart_no": "BM010",
    "order_id": [21718932, 21718938, 21718967],
    "storage_type": "1",
}
mof_packing_to_replenish = {
    "warehouse": "33",
    "tote_no": "TS0183",
    "order_id": "242504079",
    "storage_type": "6",
}
normal_packing_to_replenish = {
    "warehouse": "33",
    "tote_no": "TM0456",
    "order_id": "23836114",
    "storage_type": "1",
    "hot_tote":"H0050"
}
bulk_packing_to_replenish = {
    "warehouse": "33",
    "cart_no": "BM050",
    "order_id": [2383707902],
    "storage_type": "2",
}
one_item_packing_to_replenish = {
    "warehouse": "33",
    "cart_no": "OM45",
    "order_id": "2383755002",
    "storage_type": "2",
}

# picking
normal_picking = {
    "warehouse_number": "25",
    "storage_type": "1",
}  # 1 dry; 2 fresh; 3 frozen
bulk_picking = {"warehouse_number": "25"}
mo_picking = {"warehouse_number": "25"}
mof_picking = {"warehouse_number": "33"}
one_item_picking = {"warehouse_number": "25"}
fbw_picking = {"warehouse_number": "25", "storage_type": "2"}
alcohol_picking = {"warehouse_number": "8"}
as_picking = {"warehouse_number": "25", "storage_type": "1"}
geekPlus_picking = {"warehouse_number": "41", "storage_type": "1"}
repack_picking = {"warehouse_number": "25", "storage_type": "2"}



# route_check
route_check = {
    "warehouse": "37",
    "delivery_date": "2024-01-15",
    "route_id": 204,
    "storage_type": "3",
}

# route check move to cot
route_check_cancel = {
    "warehouse_number": "29",
    "delivery_date": "2024-03-15",
    "route_id": 2,
    "storage_type": "3",
    "tracking_num": "24250619903965",
    "order_id": "24250619903"
}

# receive
receive_info = {"warehouse_number": "20", "reference_no": "2655913", "item_number": "30702"}
receive_delete_info={"warehouse_number": "20", "reference_no": "2655914", "item_number": "96024"}
# putaway
putaway_info = {
    "warehouse_number": "20",
    "location_no": "B0432-1-1",
    "item_number": "30702"
}


# as_receive
as_receive_info = {"warehouse_number": "25", "reference_no": "2655915","item_number": "510"}
# as_putaway
as_putaway_info = {
    "warehouse_number": "25",
    "location_no": "AUTOSTORE",
    "item_number": "510"
}


# geek_receive
geek_receive_info = {"warehouse_number": "41", "reference_no": "2655916","item_number": "5660"}
# geek_putaway
geek_putaway_info = {
    "warehouse_number": "41",
    "location_no": "GEEKPLUS",
    "item_number": "5660"
}

# ro_receive
ro_receive_info = {"warehouse_number": "25", "reference_no": "RO0000001300","item_number": "44838"}
# ro_putaway
ro_putaway_info = {
    "warehouse_number": "25",
    "item_number": "44838"
}



# order type 与storage type mapping关系
storage_type_mapping = {"1": [0, 4, 5], "2": [2], "3": [3], "6": [6], "7": [7]}

# Cancel Order
cancel_order = {"warehouse_number": "29", "order_id": "17891564", "order_type": 0}

# OOS Refund Order
oos_refund_order = {"warehouse_number": "29", "order_id": "242506200", "order_type": 0}

# order item oos
partial_oos = {"warehouse_number":"20", "order_id": "242527339", "delivery_dtm": "2024-12-03", "item_number": "10395", "order_type": 0}
all_oos = {"warehouse_number":"20", "order_id": "24252829803", "delivery_dtm": "2024-12-12", "item_number": "2068", "order_type": 3}


#vendor return
vendor_order = {'warehouse_number': '25','storage_type': '2'}  # 1 dry; 2 fresh; 3 frozen
item_number = {'item_Number1':'2071574'}

#transship
ts_info = {"ship_out_warehouse": "25", "ship_in_warehouse": "20", "item_number": "44218", "location_no": "F0102-2-3", "storage_type": "3"}

#transship Receive
ts_receive_info = {'warehouse_number': '20'}

#collect extra
collect_extra_data = {"warehouse_number": "25", "location_no": "EIT108", "item_number": "10023", "upc": "2000000010023"}

#global_fbw
IOitem_number={"item_Number":"2071574"}

#restock
restock = {'warehouse_number': '48','storage_type': '1','item_number': '1495','stock_location':'B0102-3-1','bin_location':'E0999-1-2'}  # 1 dry; 2 fresh; 3 frozen


IOitem_number={"item_Number":"2846292"}